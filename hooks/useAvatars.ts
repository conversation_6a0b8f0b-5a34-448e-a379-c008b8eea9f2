import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { mockApi } from '../services/mockApi';
import { CreateAvatarRequest } from '../types';

export const useAvatars = () => {
  return useQuery({
    queryKey: ['avatars'],
    queryFn: mockApi.getAvatars,
  });
};

export const useCreateAvatar = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (request: CreateAvatarRequest) => mockApi.createAvatar(request),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['avatars'] });
    },
  });
};

export const useDeleteAvatar = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (avatarId: string) => mockApi.deleteAvatar(avatarId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['avatars'] });
    },
  });
};
