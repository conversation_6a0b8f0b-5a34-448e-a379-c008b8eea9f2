import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { mockApi } from '../services/mockApi';
import { GenerateImageRequest } from '../types';

export const useGeneratedImages = (avatarId?: string) => {
  return useQuery({
    queryKey: ['generatedImages', avatarId],
    queryFn: () => mockApi.getGeneratedImages(avatarId),
  });
};

export const useGenerateImage = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (request: GenerateImageRequest) => mockApi.generateImage(request),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['generatedImages'] });
      queryClient.invalidateQueries({ queryKey: ['generatedImages', data.data.avatarId] });
    },
  });
};
