# Firebase Authentication Setup

This guide will help you set up Firebase Authentication for your Expo app.

## Prerequisites

1. A Firebase account (free at [firebase.google.com](https://firebase.google.com))
2. Node.js and npm installed
3. Expo CLI installed

## Step 1: Create a Firebase Project

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project" or "Add project"
3. Enter your project name (e.g., "characterise-app")
4. Choose whether to enable Google Analytics (optional)
5. Click "Create project"

## Step 2: Enable Authentication

1. In your Firebase project console, click on "Authentication" in the left sidebar
2. Click on the "Get started" button
3. Go to the "Sign-in method" tab
4. Enable "Email/Password" authentication:
   - Click on "Email/Password"
   - Toggle "Enable" to ON
   - Click "Save"

## Step 3: Get Your Firebase Configuration

1. In your Firebase project console, click on the gear icon (⚙️) next to "Project Overview"
2. Select "Project settings"
3. Scroll down to the "Your apps" section
4. Click on "Add app" and select the web icon (`</>`)
5. Enter an app nickname (e.g., "characterise-web")
6. Click "Register app"
7. Copy the Firebase configuration object

## Step 4: Configure Your Expo App

1. Create a `.env` file in your project root (copy from `.env.example`):
   ```bash
   cp .env.example .env
   ```

2. Fill in your Firebase credentials in the `.env` file:
   ```
   EXPO_PUBLIC_FIREBASE_API_KEY=your-api-key-here
   EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
   EXPO_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
   EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
   EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=*********
   EXPO_PUBLIC_FIREBASE_APP_ID=your-app-id
   ```

## Step 5: Test Your Setup

1. Start your Expo development server:
   ```bash
   npm start
   ```

2. Open your app in the Expo Go app or simulator
3. You should see the authentication screen
4. Try creating a new account with email and password
5. After successful registration, you should be redirected to the main app

## Features Included

- ✅ Email/Password Authentication
- ✅ User Registration
- ✅ User Login
- ✅ Password Reset
- ✅ Logout
- ✅ Persistent Authentication (users stay logged in)
- ✅ Loading states and error handling
- ✅ Form validation

## Security Notes

- Never commit your `.env` file to version control
- The `.env` file is already added to `.gitignore`
- Use Firebase Security Rules to protect your data
- Consider enabling multi-factor authentication for production

## Troubleshooting

### "Firebase: Error (auth/configuration-not-found)"
- Make sure your `.env` file exists and has the correct Firebase credentials
- Restart your Expo development server after creating the `.env` file

### "Firebase: Error (auth/invalid-api-key)"
- Double-check your API key in the `.env` file
- Make sure there are no extra spaces or quotes

### Authentication not persisting
- This is handled automatically with AsyncStorage persistence
- Make sure you're not clearing app data during development

## Next Steps

- Set up Firebase Security Rules
- Add user profile management
- Implement social authentication (Google, Apple, etc.)
- Add email verification
- Set up Firebase Cloud Firestore for user data
