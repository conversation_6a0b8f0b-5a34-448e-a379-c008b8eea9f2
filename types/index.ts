export interface Avatar {
  id: string;
  name: string;
  description?: string;
  images: string[]; // Array of image URIs
  createdAt: Date;
  isTraining: boolean;
  trainingProgress?: number;
}

export interface GeneratedImage {
  id: string;
  avatarId: string;
  imageUri: string;
  prompt: string;
  createdAt: Date;
}

export interface User {
  id: string;
  name: string;
  email: string;
  avatars: Avatar[];
  generatedImages: GeneratedImage[];
}

export interface CreateAvatarRequest {
  name: string;
  description?: string;
  images: string[];
}

export interface GenerateImageRequest {
  avatarId: string;
  prompt: string;
  style?: string;
}

export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}
