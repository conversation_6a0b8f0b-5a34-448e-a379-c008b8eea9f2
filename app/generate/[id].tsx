import { FlashList } from '@shopify/flash-list';
import { Image } from 'expo-image';
import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useState } from 'react';
import { Alert, Dimensions, StyleSheet, TouchableOpacity, View } from 'react-native';
import { ActivityIndicator, Appbar, Button, Text, TextInput } from 'react-native-paper';
import { useAvatars } from '../../hooks/useAvatars';
import { useGenerateImage, useGeneratedImages } from '../../hooks/useImageGeneration';

const ASPECT_RATIOS = [
  { value: '1:1', label: '1:1', width: 1, height: 1 },
  { value: '9:16', label: '9:16', width: 9, height: 16 },
  { value: '16:9', label: '16:9', width: 16, height: 9 },
  { value: '3:2', label: '3:2', width: 3, height: 2 },
  { value: '4:3', label: '4:3', width: 4, height: 3 },
  { value: '5:4', label: '5:4', width: 5, height: 4 },
];

const { width: screenWidth } = Dimensions.get('window');

export default function GenerateImageScreen() {
  const router = useRouter();
  const { id } = useLocalSearchParams<{ id: string }>();
  const [prompt, setPrompt] = useState('');
  const [selectedAspectRatio, setSelectedAspectRatio] = useState('1:1');

  const { data: avatarsResponse, isLoading: avatarsLoading } = useAvatars();
  const { data: imagesResponse } = useGeneratedImages(id);
  const generateImageMutation = useGenerateImage();

  if (avatarsLoading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" />
        <Text style={styles.loadingText}>Loading avatar...</Text>
      </View>
    );
  }

  const avatar = avatarsResponse?.data.find(a => a.id === id);
  const generatedImages = imagesResponse?.data || [];

  if (!avatar) {
    return (
      <View style={styles.centered}>
        <Text>Avatar not found</Text>
        <Button onPress={() => router.back()}>Go Back</Button>
      </View>
    );
  }

  if (avatar.isTraining) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" />
        <Text variant="headlineSmall" style={styles.trainingTitle}>
          Avatar is Training
        </Text>
        <Text variant="bodyMedium" style={styles.trainingText}>
          Training Progress: {avatar.trainingProgress}%
        </Text>
        <Text variant="bodySmall" style={styles.trainingHint}>
          Please wait for training to complete before generating images.
        </Text>
        <Button onPress={() => router.back()} style={styles.backButton}>
          Go Back
        </Button>
      </View>
    );
  }

  const handleGenerate = async () => {
    if (!prompt.trim()) {
      Alert.alert('Error', 'Please enter a prompt for image generation.');
      return;
    }

    try {
      const aspectRatio = ASPECT_RATIOS.find(ar => ar.value === selectedAspectRatio);
      await generateImageMutation.mutateAsync({
        avatarId: avatar.id,
        prompt: prompt.trim(),
        aspectRatio: selectedAspectRatio,
        width: aspectRatio?.width || 1,
        height: aspectRatio?.height || 1,
      });

      Alert.alert(
        'Success',
        'Image generated successfully!',
        [{ text: 'OK' }]
      );
      setPrompt('');
    } catch (error) {
      Alert.alert('Error', 'Failed to generate image. Please try again.');
    }
  };

  // Header component for FlashList
  const renderHeader = () => (
    <View style={styles.header}>
      {/* Prompt Input Section */}
      <View style={styles.promptSection}>
        <Text style={styles.promptLabel}>Enter Prompt</Text>
        <TextInput
          value={prompt}
          onChangeText={setPrompt}
          mode="outlined"
          multiline
          numberOfLines={6}
          style={styles.promptInput}
          placeholder="Type anything..."
          contentStyle={styles.promptInputContent}
          outlineStyle={styles.promptInputOutline}
        />

        {/* Character count */}
        {/* <View style={styles.promptFooter}>
          <View style={styles.promptActions}>
            <TouchableOpacity style={styles.actionButton}>
              <Text style={styles.actionIcon}>💡</Text>
              <Text style={styles.actionText}>inspiration</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton}>
              <Text style={styles.actionIcon}>📷</Text>
              <Text style={styles.actionText}>Add Photo</Text>
            </TouchableOpacity>
          </View>
          <Text style={styles.characterCount}>{prompt.length}/300</Text>
        </View> */}
      </View>

      {/* Aspect Ratio Section */}
      <View style={styles.aspectRatioSection}>
        <Text style={styles.sectionTitle}>Ratio</Text>
        <View style={styles.aspectRatioGrid}>
          {ASPECT_RATIOS.map((ratio) => (
            <TouchableOpacity
              key={ratio.value}
              style={[
                styles.aspectRatioButton,
                selectedAspectRatio === ratio.value && styles.aspectRatioButtonSelected
              ]}
              onPress={() => setSelectedAspectRatio(ratio.value)}
            >
              <Text style={[
                styles.aspectRatioText,
                selectedAspectRatio === ratio.value && styles.aspectRatioTextSelected
              ]}>
                {ratio.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Generate Button */}
      <Button
        mode="contained"
        onPress={handleGenerate}
        style={styles.generateButton}
        contentStyle={styles.generateButtonContent}
        disabled={generateImageMutation.isPending || !prompt.trim()}
        loading={generateImageMutation.isPending}
      >
        {generateImageMutation.isPending ? 'Generating...' : 'Generate'}
      </Button>

      {/* Section title for generated images */}
      {generatedImages.length > 0 && (
        <Text style={styles.generatedImagesTitle}>Previously Generated</Text>
      )}
    </View>
  );

  // Render item for FlashList
  const renderGeneratedImage = ({ item }: { item: any }) => (
    <TouchableOpacity style={styles.generatedImageItem}>
      <Image
        source={{ uri: item.imageUri }}
        style={styles.generatedImage}
        contentFit="cover"
      />
      <View style={styles.generatedImageOverlay}>
        <Text style={styles.generatedImagePrompt} numberOfLines={2}>
          {item.prompt}
        </Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
       {/* Header */}
      <Appbar.Header elevated mode='center-aligned'>
        <Appbar.Content title="Generate" />
      </Appbar.Header>
     <View style={{height:20}}></View>
      <FlashList
        data={generatedImages}
        renderItem={renderGeneratedImage}
        keyExtractor={(item: any) => item.id}
        numColumns={2}
        estimatedItemSize={200}
        ListHeaderComponent={renderHeader}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  loadingText: {
    marginTop: 16,
  },
  trainingTitle: {
    textAlign: 'center',
    marginBottom: 8,
  },
  trainingText: {
    textAlign: 'center',
    marginBottom: 16,
  },
  trainingHint: {
    textAlign: 'center',
    opacity: 0.7,
    marginBottom: 24,
  },
  backButton: {
    marginTop: 16,
  },
  listContainer: {
    paddingHorizontal: 16,
  },
  header: {
    paddingBottom: 20,
  },

  // Prompt Section
  promptSection: {
    marginBottom: 24,
  },
  promptLabel: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000',
    marginBottom: 12,
  },
  promptInput: {
    backgroundColor: '#f8f9fa',
    minHeight: 120,
  },
  promptInputContent: {
    paddingTop: 16,
  },
  promptInputOutline: {
    borderColor: '#e9ecef',
    borderWidth: 1,
  },
  promptFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 12,
  },
  promptActions: {
    flexDirection: 'row',
    gap: 20,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  actionIcon: {
    fontSize: 16,
  },
  actionText: {
    fontSize: 14,
    color: '#6c757d',
  },
  characterCount: {
    fontSize: 14,
    color: '#6c757d',
  },

  // Aspect Ratio Section
  aspectRatioSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000',
    marginBottom: 16,
  },
  aspectRatioGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  aspectRatioButton: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 20,
    backgroundColor: '#f8f9fa',
    borderWidth: 1,
    borderColor: '#e9ecef',
    minWidth: 80,
    alignItems: 'center',
  },
  aspectRatioButtonSelected: {
    backgroundColor: '#6366f1',
    borderColor: '#6366f1',
  },
  aspectRatioText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#495057',
  },
  aspectRatioTextSelected: {
    color: '#fff',
  },

  // Generate Button
  generateButton: {
    marginBottom: 32,
    borderRadius: 25,
  },
  generateButtonContent: {
    paddingVertical: 8,
  },

  // Generated Images
  generatedImagesTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000',
    marginBottom: 16,
  },
  generatedImageItem: {
    flex: 1,
    margin: 4,
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: '#f8f9fa',
  },
  generatedImage: {
    width: '100%',
    height: 200,
  },
  generatedImageOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    padding: 12,
  },
  generatedImagePrompt: {
    color: '#fff',
    fontSize: 12,
    lineHeight: 16,
  },
});
