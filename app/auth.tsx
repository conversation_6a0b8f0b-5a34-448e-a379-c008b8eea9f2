import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import LoginScreen from '../components/auth/LoginScreen';
import SignUpScreen from '../components/auth/SignUpScreen';
import { useAppTheme } from '../contexts/ThemeContext';

export default function AuthScreen() {
  const [isLogin, setIsLogin] = useState(true);
  const { isDark } = useAppTheme();

  return (
    <SafeAreaView 
      style={[
        styles.container, 
        { backgroundColor: isDark ? '#121212' : '#f5f5f5' }
      ]}
    >
      {isLogin ? (
        <LoginScreen onSwitchToSignUp={() => setIsLogin(false)} />
      ) : (
        <SignUpScreen onSwitchToLogin={() => setIsLogin(true)} />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
