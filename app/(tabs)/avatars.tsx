import { MaterialIcons } from '@expo/vector-icons';
import { FlashList } from '@shopify/flash-list';
import { useRouter } from 'expo-router';
import React from 'react';
import { StyleSheet, View } from 'react-native';
import { ActivityIndicator, Appbar, Button, FAB, Searchbar, Text } from 'react-native-paper';
import { AvatarGridItem } from '../../components/AvatarGridItem';
import { useAppTheme } from '../../contexts/ThemeContext';
import { useAvatars } from '../../hooks/useAvatars';

export default function AvatarsScreen() {
  const router = useRouter();
  const { data: avatarsResponse, isLoading, error } = useAvatars();
  const { isDark } = useAppTheme();
  const [searchQuery, setSearchQuery] = React.useState('');

  // Filter avatars based on search query
  const filteredAvatars = React.useMemo(() => {
    if (!avatarsResponse?.data) return [];
    if (!searchQuery.trim()) return avatarsResponse.data;

    return avatarsResponse.data.filter(avatar =>
      avatar.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [avatarsResponse?.data, searchQuery]);

  if (isLoading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" />
        <Text style={styles.loadingText}>Loading avatars...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.centered}>
        <Text>Error loading avatars</Text>
      </View>
    );
  }

  const avatars = filteredAvatars;



  const renderAvatarItem = ({ item }: { item: { id: string; name: string; images: string[]; isTraining: boolean; createdAt: Date; trainingProgress?: number } }) => (
    <AvatarGridItem
      avatar={item}
      onPress={() => router.push(`/avatar/${item.id}`)}
    />
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <MaterialIcons
        name="face"
        size={80}
        color={isDark ? '#666' : '#ccc'}
        style={styles.emptyIcon}
      />
      <Text variant="headlineSmall" style={styles.emptyTitle}>
        No Avatars Yet
      </Text>
      <Text variant="bodyMedium" style={styles.emptyDescription}>
        Create your first AI avatar by uploading 5-10 photos
      </Text>
      <Button
        mode="contained"
        onPress={() => router.push('/create-avatar')}
        style={styles.createButton}
        icon="plus"
      >
        Create Avatar
      </Button>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: isDark ? '#121212' : '#fff' }]}>
      {/* Header */}
      <Appbar.Header elevated mode='center-aligned'>
        <Appbar.Content title="My Characters" />
      </Appbar.Header>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Searchbar
          placeholder="Search avatars..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
          inputStyle={styles.searchInput}
          iconColor={isDark ? '#fff' : '#666'}
        />
      </View>

      {isLoading ? (
        <View style={styles.centered}>
          <ActivityIndicator size="large" />
          <Text style={styles.loadingText}>Loading avatars...</Text>
        </View>
      ) : error ? (
        <View style={styles.centered}>
          <Text>Error loading avatars</Text>
        </View>
      ) : (
        <FlashList
          data={avatars}
          renderItem={renderAvatarItem}
          keyExtractor={(item: any) => item.id}
          numColumns={3}
          estimatedItemSize={120}
          contentContainerStyle={styles.gridContainer}
          ListEmptyComponent={renderEmptyState}
          showsVerticalScrollIndicator={false}
        />
      )}

      <FAB
        icon="plus"
        style={styles.fab}
        onPress={() => router.push('/create-avatar')}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
  },
  gridContainer: {
    padding: 16,
    flexGrow: 1,
  },
  row: {
    justifyContent: 'space-around',
    paddingHorizontal: 8,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyIcon: {
    marginBottom: 24,
  },
  emptyTitle: {
    textAlign: 'center',
    marginBottom: 8,
  },
  emptyDescription: {
    textAlign: 'center',
    marginBottom: 24,
    opacity: 0.7,
  },
  createButton: {
    marginTop: 16,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  searchBar: {
    elevation: 0,
    shadowOpacity: 0,
  },
  searchInput: {
    fontSize: 16,
  },
});
