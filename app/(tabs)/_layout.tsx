import { MaterialIcons } from '@expo/vector-icons';
import { Tabs } from 'expo-router';
import { useTheme } from 'react-native-paper';
import { useAppTheme } from '../../contexts/ThemeContext';

export default function TabLayout() {
  const theme = useTheme();
  const { isDark } = useAppTheme();

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: theme.colors.primary,
        tabBarInactiveTintColor: theme.colors.onSurfaceVariant,
        tabBarStyle: {
          backgroundColor: theme.colors.surface,
          borderTopColor: theme.colors.outline,
          borderTopWidth: 0.5,
        },
        headerStyle: {
          backgroundColor: theme.colors.surface,
        },
        headerTintColor: theme.colors.onSurface,
        headerShown: false, // We'll use custom headers in each screen
        sceneStyle: {
          backgroundColor: isDark ? '#121212' : '#ffffff',
        },
      }}
    >
      <Tabs.Screen
        name="avatars"
        options={{
          title: 'Avatars',
          tabBarIcon: ({ color, size }) => (
            <MaterialIcons name="face" size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: 'Profile',
          tabBarIcon: ({ color, size }) => (
            <MaterialIcons name="person" size={size} color={color} />
          ),
        }}
      />
    </Tabs>
  );
}
