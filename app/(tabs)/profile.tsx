import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>View, StyleSheet, View } from 'react-native';
import { Appbar, Card, Divider, List, Text } from 'react-native-paper';
import { useAppTheme } from '../../contexts/ThemeContext';
import { useAvatars } from '../../hooks/useAvatars';
import { useGeneratedImages } from '../../hooks/useImageGeneration';

export default function ProfileScreen() {
  const { data: avatarsResponse } = useAvatars();
  const { data: imagesResponse } = useGeneratedImages();
  const { themeMode, toggleTheme, isDark } = useAppTheme();

  const avatars = avatarsResponse?.data || [];
  const generatedImages = imagesResponse?.data || [];

  const handleSettingsPress = (setting: string) => {
    Alert.alert('Coming Soon', `${setting} feature will be available in a future update.`);
  };

  const getThemeIcon = () => {
    switch (themeMode) {
      case 'light':
        return 'light-mode';
      case 'dark':
        return 'dark-mode';
      case 'system':
        return 'brightness-auto';
      default:
        return 'brightness-auto';
    }
  };

  const getThemeLabel = () => {
    switch (themeMode) {
      case 'light':
        return 'Light Mode';
      case 'dark':
        return 'Dark Mode';
      case 'system':
        return 'System Default';
      default:
        return 'System Default';
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: isDark ? '#121212' : '#fff' }]}>
      {/* Header */}
      <Appbar.Header elevated>
        <Appbar.Content title="Profile" />
      </Appbar.Header>

      <ScrollView style={styles.scrollContainer}>
      <Card style={styles.card}>
        <Card.Content>
          <Text variant="headlineSmall" style={styles.title}>
            Profile Overview
          </Text>
          <Text variant="bodyMedium" style={styles.subtitle}>
            Manage your AI avatars and generated content
          </Text>
        </Card.Content>
      </Card>

      <Card style={styles.card}>
        <Card.Content>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Statistics
          </Text>
        </Card.Content>
        <List.Item
          title="Total Avatars"
          description={`${avatars.length} avatar${avatars.length !== 1 ? 's' : ''} created`}
          left={(props) => <List.Icon {...props} icon="face" />}
        />
        <List.Item
          title="Generated Images"
          description={`${generatedImages.length} image${generatedImages.length !== 1 ? 's' : ''} generated`}
          left={(props) => <List.Icon {...props} icon="image" />}
        />
        <List.Item
          title="Training Status"
          description={`${avatars.filter(a => a.isTraining).length} avatar${avatars.filter(a => a.isTraining).length !== 1 ? 's' : ''} in training`}
          left={(props) => <List.Icon {...props} icon="school" />}
        />
      </Card>

      <Card style={styles.card}>
        <Card.Content>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Recent Activity
          </Text>
        </Card.Content>
        {avatars.length > 0 ? (
          avatars.slice(0, 3).map((avatar) => (
            <List.Item
              key={avatar.id}
              title={avatar.name}
              description={`Created ${avatar.createdAt.toLocaleDateString()}`}
              left={(props) => <List.Icon {...props} icon="face" />}
              right={(props) => 
                avatar.isTraining ? (
                  <Text variant="bodySmall" style={styles.trainingText}>
                    Training...
                  </Text>
                ) : (
                  <List.Icon {...props} icon="check-circle" color="green" />
                )
              }
            />
          ))
        ) : (
          <List.Item
            title="No avatars yet"
            description="Create your first avatar to get started"
            left={(props) => <List.Icon {...props} icon="information" />}
          />
        )}
      </Card>

      <Card style={styles.card}>
        <Card.Content>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Settings
          </Text>
        </Card.Content>
        <List.Item
          title="Theme"
          description={getThemeLabel()}
          left={(props) => <List.Icon {...props} icon={getThemeIcon()} />}
          right={(props) => <List.Icon {...props} icon="chevron-right" />}
          onPress={toggleTheme}
        />
        <Divider />
        <List.Item
          title="Notifications"
          description="Manage training and generation alerts"
          left={(props) => <List.Icon {...props} icon="bell" />}
          right={(props) => <List.Icon {...props} icon="chevron-right" />}
          onPress={() => handleSettingsPress('Notifications')}
        />
        <Divider />
        <List.Item
          title="Storage"
          description="Manage your images and data"
          left={(props) => <List.Icon {...props} icon="folder" />}
          right={(props) => <List.Icon {...props} icon="chevron-right" />}
          onPress={() => handleSettingsPress('Storage')}
        />
        <Divider />
        <List.Item
          title="Help & Support"
          description="Get help with the app"
          left={(props) => <List.Icon {...props} icon="help-circle" />}
          right={(props) => <List.Icon {...props} icon="chevron-right" />}
          onPress={() => handleSettingsPress('Help & Support')}
        />
      </Card>

        <View style={styles.footer}>
          <Text variant="bodySmall" style={styles.footerText}>
            AI Avatar Generator v1.0.0
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
    padding: 16,
  },
  card: {
    marginBottom: 16,
  },
  title: {
    marginBottom: 8,
  },
  subtitle: {
    opacity: 0.7,
  },
  sectionTitle: {
    marginBottom: 8,
  },
  trainingText: {
    color: 'orange',
    alignSelf: 'center',
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 24,
  },
  footerText: {
    opacity: 0.5,
  },
});
