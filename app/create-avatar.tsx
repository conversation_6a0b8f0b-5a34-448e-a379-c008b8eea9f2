import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import GenderSelection from '../components/create-avatar/GenderSelection';
import MultipleGeneration from '../components/create-avatar/MultipleGeneration';
import PreviewGeneration from '../components/create-avatar/PreviewGeneration';
import TrainingScreen from '../components/create-avatar/TrainingScreen';
import TraitsSelection from '../components/create-avatar/TraitsSelection';
import { Gender, NavigationStep, Step } from '../components/create-avatar/types';
import UploadWorkflow from '../components/create-avatar/UploadWorkflow';
import WorkflowSelection from '../components/create-avatar/WorkflowSelection';

export default function CreateAvatarScreen() {
  const router = useRouter();

  // Stack-based navigation
  const [navigationStack, setNavigationStack] = useState<NavigationStep[]>([
    { step: 'workflow' }
  ]);

  // Current step
  const currentStep = navigationStack[navigationStack.length - 1];

  // Workflow state
  const [selectedGender, setSelectedGender] = useState<Gender>('female');
  const [selectedTraits, setSelectedTraits] = useState<Record<string, string>>({});
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  // Navigation helpers
  const pushStep = (step: Step, data?: any) => {
    setNavigationStack(prev => [...prev, { step, data }]);
  };

  const popStep = () => {
    if (navigationStack.length > 1) {
      setNavigationStack(prev => prev.slice(0, -1));
    } else {
      router.back(); // Exit if at root
    }
  };

  // Workflow handlers
  const handleSelectUpload = () => {
    pushStep('upload');
  };

  const handleSelectGenerate = () => {
    pushStep('gender');
  };

  const handleGenderChange = (gender: Gender) => {
    setSelectedGender(gender);
  };

  const handleTraitChange = (trait: string, value: string) => {
    setSelectedTraits(prev => ({ ...prev, [trait]: value }));
  };

  const handleImagesSelected = (images: string[]) => {
    setSelectedImages(images);
    pushStep('training');
  };

  const handleComplete = () => {
    router.back();
  };

  // Main render logic with component-based navigation
  switch (currentStep.step) {
    case 'workflow':
      return (
        <WorkflowSelection
          onBack={popStep}
          onSelectUpload={handleSelectUpload}
          onSelectGenerate={handleSelectGenerate}
        />
      );

    case 'gender':
      return (
        <GenderSelection
          onBack={popStep}
          selectedGender={selectedGender}
          onGenderChange={handleGenderChange}
          onContinue={() => pushStep('traits')}
        />
      );

    case 'traits':
      return (
        <TraitsSelection
          onBack={popStep}
          selectedGender={selectedGender}
          selectedTraits={selectedTraits}
          onTraitChange={handleTraitChange}
          onContinue={() => pushStep('preview')}
        />
      );

    case 'preview':
      return (
        <PreviewGeneration
          onBack={popStep}
          selectedGender={selectedGender}
          selectedTraits={selectedTraits}
          onContinue={() => pushStep('generate-multiple')}
        />
      );

    case 'generate-multiple':
      return (
        <MultipleGeneration
          onBack={popStep}
          selectedGender={selectedGender}
          selectedTraits={selectedTraits}
          onContinue={handleImagesSelected}
        />
      );

    case 'training':
      return (
        <TrainingScreen
          selectedImages={selectedImages}
          onComplete={handleComplete}
        />
      );

    case 'upload':
      return (
        <UploadWorkflow
          onBack={popStep}
          onComplete={handleComplete}
        />
      );

    default:
      return (
        <WorkflowSelection
          onBack={popStep}
          onSelectUpload={handleSelectUpload}
          onSelectGenerate={handleSelectGenerate}
        />
      );
  }
}
