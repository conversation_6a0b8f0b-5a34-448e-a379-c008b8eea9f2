import React from 'react';
import { View, ScrollView, StyleSheet, Dimensions } from 'react-native';
import { Text, Card, ActivityIndicator } from 'react-native-paper';
import { useLocalSearchParams } from 'expo-router';
import { Image } from 'expo-image';
import { useGeneratedImages } from '../../hooks/useImageGeneration';
import { useAvatars } from '../../hooks/useAvatars';

const { width } = Dimensions.get('window');
const imageSize = (width - 48) / 2; // 2 columns with padding

export default function GalleryScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { data: imagesResponse, isLoading } = useGeneratedImages(id);
  const { data: avatarsResponse } = useAvatars();

  const avatar = avatarsResponse?.data.find(a => a.id === id);
  const generatedImages = imagesResponse?.data || [];

  if (isLoading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" />
        <Text style={styles.loadingText}>Loading gallery...</Text>
      </View>
    );
  }

  if (generatedImages.length === 0) {
    return (
      <View style={styles.centered}>
        <Text variant="headlineSmall" style={styles.emptyTitle}>
          No Images Yet
        </Text>
        <Text variant="bodyMedium" style={styles.emptyDescription}>
          Generate some images to see them here
        </Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <Card style={styles.headerCard}>
        <Card.Content>
          <Text variant="headlineSmall" style={styles.title}>
            {avatar?.name} Gallery
          </Text>
          <Text variant="bodyMedium" style={styles.subtitle}>
            {generatedImages.length} generated image{generatedImages.length !== 1 ? 's' : ''}
          </Text>
        </Card.Content>
      </Card>

      <View style={styles.imageGrid}>
        {generatedImages.map((image) => (
          <Card key={image.id} style={styles.imageCard}>
            <Image
              source={{ uri: image.imageUri }}
              style={styles.image}
              contentFit="cover"
            />
            <Card.Content style={styles.imageInfo}>
              <Text variant="bodySmall" numberOfLines={2} style={styles.prompt}>
                {image.prompt}
              </Text>
              <Text variant="bodySmall" style={styles.date}>
                {image.createdAt.toLocaleDateString()}
              </Text>
            </Card.Content>
          </Card>
        ))}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  loadingText: {
    marginTop: 16,
  },
  emptyTitle: {
    textAlign: 'center',
    marginBottom: 8,
  },
  emptyDescription: {
    textAlign: 'center',
    opacity: 0.7,
  },
  headerCard: {
    marginBottom: 16,
  },
  title: {
    marginBottom: 4,
  },
  subtitle: {
    opacity: 0.7,
  },
  imageGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  imageCard: {
    width: imageSize,
    marginBottom: 16,
  },
  image: {
    width: '100%',
    height: imageSize,
  },
  imageInfo: {
    paddingTop: 8,
    paddingBottom: 8,
  },
  prompt: {
    marginBottom: 4,
  },
  date: {
    opacity: 0.7,
  },
});
