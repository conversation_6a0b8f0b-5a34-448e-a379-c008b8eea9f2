import { FlashList } from '@shopify/flash-list';
import { Image } from 'expo-image';
import { Stack, useLocalSearchParams, useRouter } from 'expo-router';
import React, { useState } from 'react';
import { Alert, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { ActivityIndicator, Button, Chip, Menu, Surface, Text, useTheme } from 'react-native-paper';
import { useAppTheme } from '../../contexts/ThemeContext';
import { useAvatars, useDeleteAvatar } from '../../hooks/useAvatars';
import { useGeneratedImages } from '../../hooks/useImageGeneration';

const getStyles = (theme: any, isDark: boolean) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
  },
  loadingText: {
    marginTop: 16,
    color: theme.colors.onBackground,
  },

  // Header styles
  header: {
    height: 300,
    position: 'relative',
    backgroundColor: isDark ? '#2d3748' : '#e5e7eb',
  },
  bannerImage: {
    width: '100%',
    height: '100%',
  },
  placeholderBanner: {
    width: '100%',
    height: '100%',
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderText: {
    fontSize: 72,
    fontWeight: 'bold',
    color: theme.colors.onPrimary,
  },
  backButton: {
    position: 'absolute',
    top: 50,
    left: 16,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backButtonText: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  menuButton: {
    position: 'absolute',
    top: 50,
    right: 16,
    zIndex: 1000,
  },
  menuButtonInner: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  menuButtonText: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
    lineHeight: 24,
  },

  // Stats styles
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 20,
    gap: 16,
    backgroundColor: theme.colors.background,
  },
  statCard: {
    flex: 1,
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
  },
  statNumber: {
    fontWeight: 'bold',
    color: theme.colors.onSurface,
    marginBottom: 4,
  },
  statLabel: {
    color: theme.colors.onSurfaceVariant,
    textAlign: 'center',
  },

  // Info section styles
  infoSection: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    alignItems: 'center',
    backgroundColor: theme.colors.background,
  },
  characterName: {
    fontWeight: 'bold',
    color: theme.colors.onBackground,
    marginBottom: 8,
    textAlign: 'center',
  },
  statusContainer: {
    marginBottom: 12,
  },
  trainingChip: {
    backgroundColor: '#f59e0b',
  },
  readyChip: {
    backgroundColor: '#10b981',
  },
  chipText: {
    color: '#fff',
    fontWeight: '600',
  },
  description: {
    color: theme.colors.onSurfaceVariant,
    textAlign: 'center',
    marginBottom: 8,
    paddingHorizontal: 16,
    lineHeight: 22,
  },
  createdDate: {
    color: theme.colors.onSurfaceVariant,
    textAlign: 'center',
  },

  // Gallery section styles
  gallerySection: {
    paddingHorizontal: 16,
    paddingVertical: 20,
    backgroundColor: theme.colors.background,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontWeight: 'bold',
    color: theme.colors.onBackground,
  },
  imageGallery: {
    marginHorizontal: -8,
  },
  galleryImage: {
    width: 120,
    height: 120,
    borderRadius: 12,
    overflow: 'hidden',
    marginHorizontal: 8,
    backgroundColor: theme.colors.surfaceVariant,
  },
  firstImage: {
    marginLeft: 0,
  },
  galleryImageContent: {
    width: '100%',
    height: '100%',
  },
  emptyGallery: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    color: theme.colors.onSurfaceVariant,
    marginBottom: 4,
  },
  emptySubtext: {
    color: theme.colors.onSurfaceVariant,
  },

  // Floating button styles
  floatingButton: {
    position: 'absolute',
    bottom: 30,
    right: 20,
    backgroundColor: theme.colors.primary,
    borderRadius: 30,
    paddingHorizontal: 20,
    paddingVertical: 15,
    flexDirection: 'row',
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    zIndex: 1000,
  },
  floatingButtonIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  floatingButtonText: {
    color: theme.colors.onPrimary,
    fontWeight: 'bold',
    fontSize: 16,
  },
});

export default function AvatarDetailScreen() {
  const router = useRouter();
  const { id } = useLocalSearchParams<{ id: string }>();
  const theme = useTheme();
  const { isDark } = useAppTheme();
  const { data: avatarsResponse, isLoading } = useAvatars();
  const { data: imagesResponse } = useGeneratedImages(id);
  const deleteAvatarMutation = useDeleteAvatar();
  const [menuVisible, setMenuVisible] = useState(false);

  const dynamicStyles = getStyles(theme, isDark);

  if (isLoading) {
    return (
      <View style={dynamicStyles.centered}>
        <ActivityIndicator size="large" />
        <Text style={dynamicStyles.loadingText}>Loading avatar...</Text>
      </View>
    );
  }

  const avatar = avatarsResponse?.data.find(a => a.id === id);
  const generatedImages = imagesResponse?.data || [];

  if (!avatar) {
    return (
      <View style={dynamicStyles.centered}>
        <Text>Avatar not found</Text>
        <Button onPress={() => router.back()}>Go Back</Button>
      </View>
    );
  }

  const handleDelete = () => {
    Alert.alert(
      'Delete Avatar',
      'Are you sure you want to delete this avatar? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteAvatarMutation.mutateAsync(avatar.id);
              Alert.alert('Success', 'Avatar deleted successfully', [
                { text: 'OK', onPress: () => router.back() }
              ]);
            } catch {
              Alert.alert('Error', 'Failed to delete avatar');
            }
          },
        },
      ]
    );
  };

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <ScrollView style={dynamicStyles.container} showsVerticalScrollIndicator={false}>
        {/* Header with Full Banner Image */}
      <View style={dynamicStyles.header}>
        {avatar.images.length > 0 ? (
          <Image
            source={{ uri: avatar.images[0] }}
            style={dynamicStyles.bannerImage}
            contentFit="cover"
          />
        ) : (
          <View style={dynamicStyles.placeholderBanner}>
            <Text style={dynamicStyles.placeholderText}>
              {avatar.name.charAt(0).toUpperCase()}
            </Text>
          </View>
        )}

        {/* Back button overlay */}
        <TouchableOpacity
          style={dynamicStyles.backButton}
          onPress={() => {
            console.log('Back button pressed');
            // Try router.back() first, fallback to navigate to avatars
            if (router.canGoBack()) {
              router.back();
            } else {
              router.push('/avatars');
            }
          }}
        >
          <Text style={dynamicStyles.backButtonText}>←</Text>
        </TouchableOpacity>

        {/* Menu button overlay - using View wrapper for better visibility */}
        <View style={dynamicStyles.menuButton}>
          <Menu
            visible={menuVisible}
            onDismiss={() => setMenuVisible(false)}
            anchor={
              <TouchableOpacity
                style={dynamicStyles.menuButtonInner}
                onPress={() => {
                  console.log('Menu button pressed');
                  setMenuVisible(true);
                }}
              >
                <Text style={dynamicStyles.menuButtonText}>⋮</Text>
              </TouchableOpacity>
            }
          >
            <Menu.Item
              onPress={() => {
                setMenuVisible(false);
                handleDelete();
              }}
              title="Delete Character"
              leadingIcon="delete"
            />
          </Menu>
        </View>
      </View>

      {/* Stats Cards */}
      <View style={dynamicStyles.statsContainer}>
        <Surface style={dynamicStyles.statCard} elevation={1}>
          <Text variant="headlineMedium" style={dynamicStyles.statNumber}>
            {avatar.images.length}
          </Text>
          <Text variant="bodyMedium" style={dynamicStyles.statLabel}>
            Training Images
          </Text>
        </Surface>

        <Surface style={dynamicStyles.statCard} elevation={1}>
          <Text variant="headlineMedium" style={dynamicStyles.statNumber}>
            {generatedImages.length}
          </Text>
          <Text variant="bodyMedium" style={dynamicStyles.statLabel}>
            Generated Images
          </Text>
        </Surface>
      </View>

      {/* Character Info */}
      <View style={dynamicStyles.infoSection}>
        <Text variant="headlineLarge" style={dynamicStyles.characterName}>
          {avatar.name}
        </Text>

        <View style={dynamicStyles.statusContainer}>
          {avatar.isTraining ? (
            <Chip icon="school" style={dynamicStyles.trainingChip} textStyle={dynamicStyles.chipText}>
              Training {avatar.trainingProgress}%
            </Chip>
          ) : (
            <Chip icon="check-circle" style={dynamicStyles.readyChip} textStyle={dynamicStyles.chipText}>
              ⭐ 5.00
            </Chip>
          )}
        </View>

        {avatar.description && (
          <Text variant="bodyLarge" style={dynamicStyles.description}>
            {avatar.description}
          </Text>
        )}

        <Text variant="bodyMedium" style={dynamicStyles.createdDate}>
          📍 Created on {avatar.createdAt.toLocaleDateString()}
        </Text>
      </View>

      {/* Recent Generated Images */}
      <View style={dynamicStyles.gallerySection}>
        <View style={dynamicStyles.sectionHeader}>
          <Text variant="titleLarge" style={dynamicStyles.sectionTitle}>
            Recent Images
          </Text>
          {generatedImages.length > 0 && (
            <Button
              mode="text"
              onPress={() => router.push(`/gallery/${avatar.id}`)}
              compact
            >
              See All
            </Button>
          )}
        </View>

        {generatedImages.length > 0 ? (
          <FlashList
            data={generatedImages.slice(0, 6)}
            horizontal
            showsHorizontalScrollIndicator={false}
            estimatedItemSize={120}
            renderItem={({ item, index }: { item: any; index: number }) => (
              <TouchableOpacity
                key={item.id}
                style={[dynamicStyles.galleryImage, index === 0 && dynamicStyles.firstImage]}
                onPress={() => router.push(`/gallery/${avatar.id}`)}
              >
                <Image
                  source={{ uri: item.imageUri }}
                  style={dynamicStyles.galleryImageContent}
                  contentFit="cover"
                />
              </TouchableOpacity>
            )}
            contentContainerStyle={{ paddingHorizontal: 8 }}
          />
        ) : (
          <View style={dynamicStyles.emptyGallery}>
            <Text variant="bodyMedium" style={dynamicStyles.emptyText}>
              No images generated yet
            </Text>
            <Text variant="bodySmall" style={dynamicStyles.emptySubtext}>
              Generate your first image to see it here
            </Text>
          </View>
        )}
      </View>

    </ScrollView>

    {/* Floating Generate Button */}
    <TouchableOpacity
      style={dynamicStyles.floatingButton}
      onPress={() => router.push(`/generate/${avatar.id}`)}
      disabled={avatar.isTraining}
    >
      <Text style={dynamicStyles.floatingButtonIcon}>📷</Text>
      <Text style={dynamicStyles.floatingButtonText}>
        {avatar.isTraining ? 'Training...' : 'Generate'}
      </Text>
    </TouchableOpacity>
    </>
  );
}
