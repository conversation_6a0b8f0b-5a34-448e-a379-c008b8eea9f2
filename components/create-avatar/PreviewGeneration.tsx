import { Image } from 'expo-image';
import React, { useState } from 'react';
import { <PERSON><PERSON>, ScrollView, StyleSheet, View } from 'react-native';
import { <PERSON>ppbar, Button, Surface, Text, useTheme } from 'react-native-paper';

type Gender = 'male' | 'female';

interface PreviewGenerationProps {
  onBack: () => void;
  selectedGender: Gender;
  selectedTraits: Record<string, string>;
  onContinue: () => void;
}

export default function PreviewGeneration({ 
  onBack, 
  selectedGender, 
  selectedTraits, 
  onContinue 
}: PreviewGenerationProps) {
  const theme = useTheme();
  const styles = getStyles(theme);
  
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [isGeneratingPreview, setIsGeneratingPreview] = useState(false);

  const generatePreviewImage = async () => {
    setIsGeneratingPreview(true);
    try {
      // Mock image generation - in real app would call AI service
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Generate realistic avatar placeholder based on gender and traits
      const genderParam = selectedGender === 'female' ? 'women' : 'men';
      const seed = Object.values(selectedTraits).join('').length + Date.now();
      const mockImage = `https://randomuser.me/api/portraits/${genderParam}/${seed % 99}.jpg`;
      setPreviewImage(mockImage);
    } catch {
      Alert.alert('Error', 'Failed to generate preview image. Please try again.');
    } finally {
      setIsGeneratingPreview(false);
    }
  };

  const regenerateImage = () => {
    setPreviewImage(null);
    generatePreviewImage();
  };

  // Auto-generate on first load
  if (!previewImage && !isGeneratingPreview) {
    setTimeout(() => generatePreviewImage(), 100);
  }

  return (
    <View style={styles.container}>
      <Appbar.Header elevated mode="center-aligned">
        <Appbar.BackAction onPress={onBack} />
        <Appbar.Content title="Preview Avatar" />
      </Appbar.Header>
      
      <ScrollView style={styles.content}>
        <Text style={styles.title}>Preview Your Avatar</Text>
        <Text style={styles.subtitle}>
          Here&apos;s a preview of your avatar. If you like it, we&apos;ll generate more images for training.
        </Text>
        
        <View style={styles.previewContainer}>
          {isGeneratingPreview ? (
            <View style={styles.previewPlaceholder}>
              <Text style={styles.loadingIcon}>🎨</Text>
              <Text style={styles.previewPlaceholderText}>Generating preview...</Text>
              <Text style={styles.loadingSubtext}>
                Creating your avatar based on selected traits
              </Text>
              <Button loading mode="text">Generating</Button>
            </View>
          ) : previewImage ? (
            <View style={styles.previewImageContainer}>
              <Surface style={styles.previewImageSurface} elevation={4}>
                <Image 
                  source={previewImage} 
                  alt="Avatar Preview"
                  style={{
                    width: '100%',
                    height: '100%',
                    borderRadius: 16,
                  }}
                  contentFit="cover"
                />
              </Surface>
            </View>
          ) : null}
        </View>
        
        <View style={styles.previewActions}>
          <Button
            mode="outlined"
            onPress={regenerateImage}
            style={styles.previewButton}
            disabled={isGeneratingPreview}
          >
            Regenerate
          </Button>
          
          <Button
            mode="contained"
            onPress={onContinue}
            style={styles.previewButton}
            disabled={isGeneratingPreview || !previewImage}
          >
            Looks Good!
          </Button>
        </View>
      </ScrollView>
    </View>
  );
}

const getStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.onBackground,
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.onSurfaceVariant,
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 22,
  },
  previewContainer: {
    alignItems: 'center',
    marginVertical: 32,
  },
  previewPlaceholder: {
    width: 300,
    height: 300,
    borderRadius: 16,
    backgroundColor: theme.colors.surfaceVariant,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: theme.colors.outline,
    borderStyle: 'dashed',
  },
  previewPlaceholderText: {
    fontSize: 16,
    color: theme.colors.onSurfaceVariant,
    marginBottom: 8,
  },
  loadingIcon: {
    fontSize: 32,
    marginBottom: 12,
  },
  loadingSubtext: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
    textAlign: 'center',
    marginBottom: 16,
    opacity: 0.8,
  },
  previewImageContainer: {
    alignItems: 'center',
  },
  previewImageSurface: {
    width: 300,
    height: 300,
    borderRadius: 16,
    overflow: 'hidden',
    backgroundColor: theme.colors.surface,
  },
  previewActions: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 32,
  },
  previewButton: {
    flex: 1,
    borderRadius: 25,
  },
});
