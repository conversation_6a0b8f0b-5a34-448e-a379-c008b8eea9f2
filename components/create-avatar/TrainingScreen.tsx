import { Image } from 'expo-image';
import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Sc<PERSON>View, StyleSheet, View } from 'react-native';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Card, Text, useTheme } from 'react-native-paper';

interface TrainingScreenProps {
  selectedImages: string[];
  onComplete: () => void;
}

export default function TrainingScreen({ 
  selectedImages, 
  onComplete 
}: TrainingScreenProps) {
  const theme = useTheme();
  const styles = getStyles(theme);
  
  const [trainingProgress, setTrainingProgress] = useState(0);
  const [trainingStatus, setTrainingStatus] = useState('Preparing...');

  // Training progress effect
  useEffect(() => {
    // Reset progress when component mounts
    setTrainingProgress(0);
    setTrainingStatus('Initializing training environment...');
    
    const interval = setInterval(() => {
      setTrainingProgress(prev => {
        // More realistic progress increments
        const increment = Math.random() * 3 + 1; // 1-4% increments
        const newProgress = Math.min(prev + increment, 100);
        
        if (newProgress >= 100) {
          clearInterval(interval);
          setTrainingStatus('Training Complete! 🎉');
          return 100;
        }
        
        // More detailed status updates based on progress
        if (newProgress < 10) setTrainingStatus('Initializing training environment...');
        else if (newProgress < 20) setTrainingStatus('Processing uploaded images...');
        else if (newProgress < 35) setTrainingStatus('Analyzing facial features...');
        else if (newProgress < 50) setTrainingStatus('Training neural network layers...');
        else if (newProgress < 65) setTrainingStatus('Learning character traits...');
        else if (newProgress < 80) setTrainingStatus('Fine-tuning model parameters...');
        else if (newProgress < 95) setTrainingStatus('Optimizing avatar generation...');
        else setTrainingStatus('Finalizing your avatar...');
        
        return newProgress;
      });
    }, 800); // Slower updates for more realistic feel
    
    return () => clearInterval(interval);
  }, []);

  return (
    <View style={styles.container}>
      <Appbar.Header elevated mode="center-aligned">
        <Appbar.Content title="Training Avatar" />
      </Appbar.Header>
      
      <ScrollView style={styles.content}>
        <Text style={styles.title}>Training Your Avatar</Text>
        <Text style={styles.subtitle}>
          Please wait while we train your AI avatar. This usually takes 5-10 minutes.
        </Text>
        
        {/* Training Images Preview */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.confirmationTitle}>Training Images</Text>
            <View style={styles.trainingImagesPreview}>
              {selectedImages.slice(0, 4).map((image, index) => (
                <View key={index} style={styles.trainingImageContainer}>
                  <Image 
                    source={image} 
                    alt={`Training Image ${index + 1}`}
                    style={{
                      width: 60,
                      height: 60,
                      borderRadius: 8,
                    }}
                    contentFit="cover"
                  />
                </View>
              ))}
              {selectedImages.length > 4 && (
                <View style={styles.trainingImageContainer}>
                  <View style={styles.moreImagesIndicator}>
                    <Text style={styles.moreImagesText}>
                      +{selectedImages.length - 4}
                    </Text>
                  </View>
                </View>
              )}
            </View>
            <Text style={styles.imageHint}>
              Using {selectedImages.length} selected images for training
            </Text>
          </Card.Content>
        </Card>
        
        {/* Progress Card */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.confirmationTitle}>Training Progress</Text>
            <Text style={styles.trainingStatus}>{trainingStatus}</Text>
            
            <View style={styles.progressContainer}>
              <Text style={styles.progressPercentage}>
                {Math.round(trainingProgress)}%
              </Text>
              <View style={styles.progressBarContainer}>
                <View style={[
                  styles.progressBar,
                  { width: `${trainingProgress}%` }
                ]} />
              </View>
            </View>
            
            {trainingProgress < 100 && (
              <View style={styles.trainingSteps}>
                <Text style={styles.trainingStepsTitle}>Training Steps:</Text>
                <Text style={styles.trainingStep}>
                  ✓ Image preprocessing
                </Text>
                <Text style={[
                  styles.trainingStep,
                  trainingProgress > 20 && styles.trainingStepComplete
                ]}>
                  {trainingProgress > 20 ? '✓' : '○'} Feature extraction
                </Text>
                <Text style={[
                  styles.trainingStep,
                  trainingProgress > 50 && styles.trainingStepComplete
                ]}>
                  {trainingProgress > 50 ? '✓' : '○'} Neural network training
                </Text>
                <Text style={[
                  styles.trainingStep,
                  trainingProgress > 80 && styles.trainingStepComplete
                ]}>
                  {trainingProgress > 80 ? '✓' : '○'} Model optimization
                </Text>
              </View>
            )}
          </Card.Content>
        </Card>
        
        {trainingProgress >= 100 && (
          <Card style={styles.card}>
            <Card.Content>
              <View style={styles.successContainer}>
                <Text style={styles.successIcon}>🎉</Text>
                <Text style={styles.successTitle}>Training Complete!</Text>
                <Text style={styles.successMessage}>
                  Your AI avatar has been successfully trained and is ready to use.
                </Text>
              </View>
              <Button
                mode="contained"
                onPress={() => {
                  Alert.alert(
                    'Success!',
                    'Your avatar has been trained successfully! You can now use it to generate images.',
                    [{ text: 'OK', onPress: onComplete }]
                  );
                }}
                style={styles.continueButton}
                icon="check"
              >
                Complete Training
              </Button>
            </Card.Content>
          </Card>
        )}
      </ScrollView>
    </View>
  );
}

const getStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.onBackground,
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.onSurfaceVariant,
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 22,
  },
  card: {
    marginBottom: 16,
    borderRadius: 12,
  },
  confirmationTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.onSurface,
    marginBottom: 16,
  },
  trainingImagesPreview: {
    flexDirection: 'row',
    gap: 8,
    marginVertical: 12,
  },
  trainingImageContainer: {
    width: 60,
    height: 60,
    borderRadius: 8,
    overflow: 'hidden',
  },
  moreImagesIndicator: {
    width: 60,
    height: 60,
    borderRadius: 8,
    backgroundColor: theme.colors.surfaceVariant,
    justifyContent: 'center',
    alignItems: 'center',
  },
  moreImagesText: {
    fontSize: 12,
    fontWeight: '600',
    color: theme.colors.onSurfaceVariant,
  },
  imageHint: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
    marginBottom: 16,
  },
  trainingStatus: {
    fontSize: 16,
    color: theme.colors.onSurface,
    marginBottom: 16,
    fontWeight: '500',
  },
  progressContainer: {
    marginVertical: 16,
  },
  progressPercentage: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.primary,
    textAlign: 'center',
    marginBottom: 8,
  },
  progressBarContainer: {
    height: 12,
    backgroundColor: theme.colors.surfaceVariant,
    borderRadius: 6,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: theme.colors.primary,
    borderRadius: 6,
  },
  trainingSteps: {
    marginTop: 16,
  },
  trainingStepsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.onSurface,
    marginBottom: 8,
  },
  trainingStep: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
    marginBottom: 4,
  },
  trainingStepComplete: {
    color: theme.colors.primary,
  },
  successContainer: {
    alignItems: 'center',
    marginVertical: 16,
  },
  successIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  successTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.primary,
    marginBottom: 8,
    textAlign: 'center',
  },
  successMessage: {
    fontSize: 16,
    color: theme.colors.onSurface,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 16,
  },
  continueButton: {
    marginTop: 20,
    borderRadius: 25,
  },
});
