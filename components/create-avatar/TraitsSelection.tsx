import React from 'react';
import { <PERSON>rollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { <PERSON>ppbar, Button, Text, useTheme } from 'react-native-paper';

type Gender = 'male' | 'female';

// Physical traits for each gender
const MALE_TRAITS = {
  hairStyle: ['Short', 'Medium', '<PERSON>', 'Bald', 'Curly', 'Straight'],
  hairColor: ['<PERSON>', '<PERSON>', '<PERSON>lon<PERSON>', '<PERSON>', '<PERSON>', 'White'],
  eyeColor: ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
  skinTone: ['Light', 'Medium', 'Tan', 'Dark'],
  faceShape: ['Oval', 'Round', 'Square', 'Heart', 'Long'],
  bodyType: ['Slim', 'Athletic', 'Average', 'Muscular', 'Heavy'],
};

const FEMALE_TRAITS = {
  hairStyle: ['Short', 'Medium', 'Long', 'Curly', 'Straight', 'Wavy'],
  hairColor: ['<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
  eyeColor: ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
  skinTone: ['Light', 'Medium', 'Tan', 'Dark'],
  faceShape: ['Oval', 'Round', 'Square', 'Heart', 'Long'],
  bodyType: ['Slim', 'Athletic', 'Average', 'Curvy', 'Heavy'],
};

interface TraitsSelectionProps {
  onBack: () => void;
  selectedGender: Gender;
  selectedTraits: Record<string, string>;
  onTraitChange: (trait: string, value: string) => void;
  onContinue: () => void;
}

export default function TraitsSelection({ 
  onBack, 
  selectedGender, 
  selectedTraits, 
  onTraitChange, 
  onContinue 
}: TraitsSelectionProps) {
  const theme = useTheme();
  const styles = getStyles(theme);
  const traits = selectedGender === 'male' ? MALE_TRAITS : FEMALE_TRAITS;

  const renderTraitSection = (traitKey: string, traitOptions: string[]) => (
    <View key={traitKey} style={styles.traitSection}>
      <Text style={styles.traitTitle}>
        {traitKey.charAt(0).toUpperCase() + traitKey.slice(1).replace(/([A-Z])/g, ' $1')}
      </Text>
      <View style={styles.traitOptions}>
        {traitOptions.map((option) => (
          <TouchableOpacity
            key={option}
            style={[
              styles.traitOption,
              selectedTraits[traitKey] === option && styles.traitOptionSelected
            ]}
            onPress={() => onTraitChange(traitKey, option)}
          >
            <Text style={[
              styles.traitOptionText,
              selectedTraits[traitKey] === option && styles.traitOptionTextSelected
            ]}>
              {option}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const isComplete = Object.keys(selectedTraits).length >= Object.keys(traits).length;

  return (
    <View style={styles.container}>
      <Appbar.Header elevated mode="center-aligned">
        <Appbar.BackAction onPress={onBack} />
        <Appbar.Content title="Customize Traits" />
      </Appbar.Header>
      
      <ScrollView style={styles.content}>
        <Text style={styles.title}>Physical Traits</Text>
        <Text style={styles.subtitle}>
          Customize your {selectedGender} avatar&apos;s appearance
        </Text>
        
        {Object.entries(traits).map(([traitKey, traitOptions]) =>
          renderTraitSection(traitKey, traitOptions)
        )}
        
        <Button
          mode="contained"
          onPress={onContinue}
          style={styles.continueButton}
          disabled={!isComplete}
        >
          Generate Preview
        </Button>
      </ScrollView>
    </View>
  );
}

const getStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.onBackground,
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.onSurfaceVariant,
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 22,
  },
  traitSection: {
    marginBottom: 24,
  },
  traitTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.onBackground,
    marginBottom: 12,
  },
  traitOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  traitOption: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: theme.colors.surfaceVariant,
    borderWidth: 1,
    borderColor: theme.colors.outline,
  },
  traitOptionSelected: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  traitOptionText: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
    fontWeight: '500',
  },
  traitOptionTextSelected: {
    color: theme.colors.onPrimary,
  },
  continueButton: {
    marginTop: 20,
    borderRadius: 25,
  },
});
