import React from 'react';
import { ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { Appbar, Button, Text, useTheme } from 'react-native-paper';

type Gender = 'male' | 'female';

interface GenderSelectionProps {
  onBack: () => void;
  selectedGender: Gender;
  onGenderChange: (gender: Gender) => void;
  onContinue: () => void;
}

export default function GenderSelection({ 
  onBack, 
  selectedGender, 
  onGenderChange, 
  onContinue 
}: GenderSelectionProps) {
  const theme = useTheme();
  const styles = getStyles(theme);

  return (
    <View style={styles.container}>
      <Appbar.Header elevated mode="center-aligned">
        <Appbar.BackAction onPress={onBack} />
        <Appbar.Content title="Select Gender" />
      </Appbar.Header>
      
      <ScrollView style={styles.content}>
        <Text style={styles.title}>Choose Gender</Text>
        <Text style={styles.subtitle}>
          Select the gender for your avatar to customize physical traits
        </Text>
        
        <View style={styles.genderOptions}>
          <TouchableOpacity
            style={[
              styles.genderCard,
              selectedGender === 'female' && styles.genderCardSelected
            ]}
            onPress={() => onGenderChange('female')}
          >
            <Text style={styles.genderIcon}>👩</Text>
            <Text style={styles.genderTitle}>Female</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.genderCard,
              selectedGender === 'male' && styles.genderCardSelected
            ]}
            onPress={() => onGenderChange('male')}
          >
            <Text style={styles.genderIcon}>👨</Text>
            <Text style={styles.genderTitle}>Male</Text>
          </TouchableOpacity>
        </View>
        
        <Button
          mode="contained"
          onPress={onContinue}
          style={styles.continueButton}
        >
          Continue to Traits
        </Button>
      </ScrollView>
    </View>
  );
}

const getStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.onBackground,
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.onSurfaceVariant,
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 22,
  },
  genderOptions: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 32,
  },
  genderCard: {
    flex: 1,
    padding: 24,
    borderRadius: 16,
    backgroundColor: theme.colors.surfaceVariant,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  genderCardSelected: {
    backgroundColor: theme.colors.primaryContainer,
    borderColor: theme.colors.primary,
  },
  genderIcon: {
    fontSize: 48,
    marginBottom: 12,
  },
  genderTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.onSurfaceVariant,
  },
  continueButton: {
    marginTop: 20,
    borderRadius: 25,
  },
});
