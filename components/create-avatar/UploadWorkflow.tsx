import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>rollView, StyleSheet, View } from 'react-native';
import { <PERSON><PERSON><PERSON>, <PERSON>ton, Card, Chip, Text, TextInput, useTheme } from 'react-native-paper';
import { useCreateAvatar } from '../../hooks/useAvatars';

interface UploadWorkflowProps {
  onBack: () => void;
  onComplete: () => void;
}

export default function UploadWorkflow({ 
  onBack, 
  onComplete 
}: UploadWorkflowProps) {
  const theme = useTheme();
  const styles = getStyles(theme);
  const createAvatarMutation = useCreateAvatar();
  
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [images, setImages] = useState<string[]>([]);

  const pickImages = async () => {
    // Mock image picker - in real app would use expo-image-picker
    const mockImages = Array.from({ length: Math.floor(Math.random() * 6) + 5 }, (_, i) => 
      `https://picsum.photos/200/200?random=${Date.now() + i}`
    );
    setImages(mockImages);
  };

  const removeImage = (index: number) => {
    setImages(prev => prev.filter((_, i) => i !== index));
  };

  const handleCreate = async () => {
    if (!name.trim()) {
      Alert.alert('Error', 'Please enter a name for your avatar.');
      return;
    }

    if (images.length < 5) {
      Alert.alert('Error', 'Please upload at least 5 images.');
      return;
    }

    try {
      await createAvatarMutation.mutateAsync({
        name: name.trim(),
        description: description.trim() || undefined,
        images,
      });

      Alert.alert(
        'Success',
        'Avatar created successfully! Training has started and will take a few minutes.',
        [{ text: 'OK', onPress: onComplete }]
      );
    } catch {
      Alert.alert('Error', 'Failed to create avatar. Please try again.');
    }
  };

  return (
    <View style={styles.container}>
      <Appbar.Header elevated mode="center-aligned">
        <Appbar.BackAction onPress={onBack} />
        <Appbar.Content title="Upload Images" />
      </Appbar.Header>
      
      <ScrollView style={styles.content}>
        <Text style={styles.title}>Upload Training Images</Text>
        <Text style={styles.subtitle}>
          Upload 5-10 high-quality photos to train your AI avatar
        </Text>
        
        <Card style={styles.card}>
          <Card.Content>
            <TextInput
              label="Avatar Name"
              value={name}
              onChangeText={setName}
              mode="outlined"
              style={styles.input}
              placeholder="e.g., Professional Headshots"
            />
            
            <TextInput
              label="Description (Optional)"
              value={description}
              onChangeText={setDescription}
              mode="outlined"
              multiline
              numberOfLines={3}
              style={styles.input}
              placeholder="Describe the style or purpose of this avatar"
            />
          </Card.Content>
        </Card>

        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.sectionTitle}>
              Upload Images ({images.length}/10)
            </Text>
            <Text style={styles.imageHint}>
              Upload at least 5 images. Better results with varied angles and lighting.
            </Text>
            
            <Button
              mode="outlined"
              onPress={pickImages}
              style={styles.uploadButton}
              icon="camera"
              disabled={images.length >= 10}
            >
              {images.length === 0 ? 'Select Images' : 'Add More Images'}
            </Button>

            {images.length > 0 && (
              <View style={styles.imageChips}>
                {images.map((_, index) => (
                  <Chip
                    key={index}
                    onClose={() => removeImage(index)}
                    style={styles.imageChip}
                  >
                    Image {index + 1}
                  </Chip>
                ))}
              </View>
            )}
          </Card.Content>
        </Card>

        <View style={styles.confirmationActions}>
          <Button
            mode="outlined"
            onPress={onBack}
            style={styles.confirmationButton}
            disabled={createAvatarMutation.isPending}
          >
            Back
          </Button>
          
          <Button
            mode="contained"
            onPress={handleCreate}
            style={styles.confirmationButton}
            disabled={createAvatarMutation.isPending || images.length < 5 || !name.trim()}
            loading={createAvatarMutation.isPending}
          >
            {createAvatarMutation.isPending ? 'Creating...' : 'Create Avatar'}
          </Button>
        </View>
      </ScrollView>
    </View>
  );
}

const getStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.onBackground,
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.onSurfaceVariant,
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 22,
  },
  card: {
    marginBottom: 16,
    borderRadius: 12,
  },
  input: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.onBackground,
    marginBottom: 8,
  },
  imageHint: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
    marginBottom: 16,
  },
  uploadButton: {
    marginBottom: 16,
  },
  imageChips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  imageChip: {
    marginBottom: 8,
  },
  confirmationActions: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 20,
  },
  confirmationButton: {
    flex: 1,
    borderRadius: 25,
  },
});
