import { Image } from 'expo-image';
import React, { useState } from 'react';
import { <PERSON><PERSON>, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { <PERSON>pp<PERSON>, <PERSON><PERSON>, Card, Text, useTheme } from 'react-native-paper';

type Gender = 'male' | 'female';

interface MultipleGenerationProps {
  onBack: () => void;
  selectedGender: Gender;
  selectedTraits: Record<string, string>;
  onContinue: (selectedImages: string[]) => void;
}

export default function MultipleGeneration({ 
  onBack, 
  selectedGender, 
  selectedTraits, 
  onContinue 
}: MultipleGenerationProps) {
  const theme = useTheme();
  const styles = getStyles(theme);
  
  const [numberOfImages, setNumberOfImages] = useState(8);
  const [generatedImages, setGeneratedImages] = useState<string[]>([]);
  const [isGeneratingMultiple, setIsGeneratingMultiple] = useState(false);
  const [selectedImages, setSelectedImages] = useState<string[]>([]);

  const generateMultipleImages = async () => {
    setIsGeneratingMultiple(true);
    try {
      // Mock multiple image generation with realistic avatars
      await new Promise(resolve => setTimeout(resolve, 5000));
      const genderParam = selectedGender === 'female' ? 'women' : 'men';
      const mockImages = Array.from({ length: numberOfImages }, (_, i) => {
        const seed = (Object.values(selectedTraits).join('').length + Date.now() + i) % 99;
        return `https://randomuser.me/api/portraits/${genderParam}/${seed}.jpg`;
      });
      setGeneratedImages(mockImages);
    } catch {
      Alert.alert('Error', 'Failed to generate images. Please try again.');
    } finally {
      setIsGeneratingMultiple(false);
    }
  };

  const toggleImageSelection = (image: string) => {
    setSelectedImages(prev => 
      prev.includes(image) 
        ? prev.filter(img => img !== image)
        : [...prev, image]
    );
  };

  const proceedToTraining = () => {
    if (selectedImages.length === 0) {
      Alert.alert('Error', 'Please select at least one image for training.');
      return;
    }
    onContinue(selectedImages);
  };

  return (
    <View style={styles.container}>
      <Appbar.Header elevated mode="center-aligned">
        <Appbar.BackAction onPress={onBack} />
        <Appbar.Content title="Generate Training Images" />
      </Appbar.Header>
      
      <ScrollView style={styles.content}>
        <Text style={styles.title}>Generate Training Images</Text>
        <Text style={styles.subtitle}>
          Choose how many images to generate. More images = better results!
        </Text>
        
        <View style={styles.imageCountContainer}>
          <Text style={styles.imageCountTitle}>Number of Images</Text>
          <Text style={styles.imageCountSubtitle}>
            Recommended: 8-12 images for best training results
          </Text>
          
          <View style={styles.imageCountSlider}>
            <Text style={styles.imageCountValue}>{numberOfImages} images</Text>
            <View style={{ flexDirection: 'row', gap: 8, marginTop: 16 }}>
              {[4, 6, 8, 10, 12].map(count => (
                <Button
                  key={count}
                  mode={numberOfImages === count ? 'contained' : 'outlined'}
                  onPress={() => setNumberOfImages(count)}
                  compact
                >
                  {count}
                </Button>
              ))}
            </View>
          </View>
        </View>
        
        {generatedImages.length === 0 ? (
          <>
            {isGeneratingMultiple && (
              <Card style={styles.card}>
                <Card.Content>
                  <View style={styles.loadingContainer}>
                    <Text style={styles.loadingIcon}>🤖</Text>
                    <Text style={styles.loadingTitle}>Generating {numberOfImages} Images</Text>
                    <Text style={styles.loadingSubtext}>
                      Creating diverse avatar variations for training...
                    </Text>
                    <Text style={styles.loadingProgress}>
                      This may take a few moments
                    </Text>
                  </View>
                </Card.Content>
              </Card>
            )}
            <Button
              mode="contained"
              onPress={generateMultipleImages}
              loading={isGeneratingMultiple}
              disabled={isGeneratingMultiple}
              style={styles.continueButton}
            >
              {isGeneratingMultiple ? 'Generating Images...' : 'Generate Images'}
            </Button>
          </>
        ) : (
          <>
            <Text style={styles.sectionTitle}>
              Select Images for Training ({selectedImages.length}/{generatedImages.length})
            </Text>
            <Text style={styles.imageHint}>
              Tap images to select them for avatar training
            </Text>
            
            <View style={styles.imagesGrid}>
              {generatedImages.map((image, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.imageGridItem,
                    selectedImages.includes(image) && styles.imageGridItemSelected
                  ]}
                  onPress={() => toggleImageSelection(image)}
                >
                  <Image 
                    source={image} 
                    alt={`Generated Avatar ${index + 1}`}
                    style={{
                      width: '100%',
                      height: '100%',
                      borderRadius: 12,
                    }}
                    contentFit="cover"
                  />
                  {selectedImages.includes(image) && (
                    <View style={styles.imageSelectionOverlay}>
                      <Text style={styles.imageSelectionCheck}>✓</Text>
                    </View>
                  )}
                </TouchableOpacity>
              ))}
            </View>
            
            <View style={styles.confirmationActions}>
              <Button
                mode="outlined"
                onPress={() => {
                  setGeneratedImages([]);
                  setSelectedImages([]);
                }}
                style={styles.confirmationButton}
              >
                Regenerate
              </Button>
              
              <Button
                mode="contained"
                onPress={proceedToTraining}
                style={styles.confirmationButton}
                disabled={selectedImages.length === 0}
              >
                Start Training ({selectedImages.length})
              </Button>
            </View>
          </>
        )}
      </ScrollView>
    </View>
  );
}

const getStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.onBackground,
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.onSurfaceVariant,
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 22,
  },
  imageCountContainer: {
    marginVertical: 24,
  },
  imageCountTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.onBackground,
    marginBottom: 8,
  },
  imageCountSubtitle: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
    marginBottom: 16,
  },
  imageCountSlider: {
    marginVertical: 16,
  },
  imageCountValue: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.primary,
    textAlign: 'center',
  },
  card: {
    marginBottom: 16,
    borderRadius: 12,
  },
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  loadingIcon: {
    fontSize: 32,
    marginBottom: 12,
  },
  loadingTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.onSurface,
    marginBottom: 8,
    textAlign: 'center',
  },
  loadingSubtext: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
    textAlign: 'center',
    marginBottom: 16,
    opacity: 0.8,
  },
  loadingProgress: {
    fontSize: 12,
    color: theme.colors.onSurfaceVariant,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  continueButton: {
    marginTop: 20,
    borderRadius: 25,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.onBackground,
    marginBottom: 8,
  },
  imageHint: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
    marginBottom: 16,
  },
  imagesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginVertical: 16,
  },
  imageGridItem: {
    width: '48%',
    aspectRatio: 1,
    borderRadius: 12,
    backgroundColor: theme.colors.surfaceVariant,
    borderWidth: 2,
    borderColor: 'transparent',
    overflow: 'hidden',
  },
  imageGridItemSelected: {
    borderColor: theme.colors.primary,
  },
  imageSelectionOverlay: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageSelectionCheck: {
    color: theme.colors.onPrimary,
    fontSize: 16,
    fontWeight: 'bold',
  },
  confirmationActions: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 20,
  },
  confirmationButton: {
    flex: 1,
    borderRadius: 25,
  },
});
