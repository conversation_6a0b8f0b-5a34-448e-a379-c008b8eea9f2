import React from 'react';
import { ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { Appbar, Surface, Text, useTheme } from 'react-native-paper';

interface WorkflowSelectionProps {
  onBack: () => void;
  onSelectUpload: () => void;
  onSelectGenerate: () => void;
}

export default function WorkflowSelection({ 
  onBack, 
  onSelectUpload, 
  onSelectGenerate 
}: WorkflowSelectionProps) {
  const theme = useTheme();
  const styles = getStyles(theme);

  return (
    <View style={styles.container}>
      <Appbar.Header elevated mode="center-aligned">
        <Appbar.BackAction onPress={onBack} />
        <Appbar.Content title="Create Avatar" />
      </Appbar.Header>
      
      <ScrollView style={styles.content}>
        <Text style={styles.title}>Choose Creation Method</Text>
        <Text style={styles.subtitle}>
          Select how you&apos;d like to create your AI avatar
        </Text>
        
        <View style={styles.workflowOptions}>
          <TouchableOpacity
            style={styles.workflowCard}
            onPress={onSelectUpload}
          >
            <Surface style={styles.workflowSurface} elevation={2}>
              <Text style={styles.workflowIcon}>📷</Text>
              <Text style={styles.workflowTitle}>Upload Images/AI Clone</Text>
              <Text style={styles.workflowDescription}>
                Upload 5-10 photos to create a personalized AI avatar
              </Text>
            </Surface>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.workflowCard}
            onPress={onSelectGenerate}
          >
            <Surface style={styles.workflowSurface} elevation={2}>
              <Text style={styles.workflowIcon}>⚙️</Text>
              <Text style={styles.workflowTitle}>Generate Avatar</Text>
              <Text style={styles.workflowDescription}>
                Use our configurator to design your ideal avatar
              </Text>
            </Surface>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
}

const getStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.onBackground,
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.onSurfaceVariant,
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 22,
  },
  workflowOptions: {
    gap: 20,
  },
  workflowCard: {
    marginBottom: 16,
  },
  workflowSurface: {
    padding: 24,
    borderRadius: 16,
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
  },
  workflowIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  workflowTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.onSurface,
    marginBottom: 8,
    textAlign: 'center',
  },
  workflowDescription: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
    textAlign: 'center',
    lineHeight: 20,
  },
});
