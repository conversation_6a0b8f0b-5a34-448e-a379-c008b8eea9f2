// Shared types for create avatar workflow

export type WorkflowType = 'upload' | 'generate' | null;
export type Gender = 'male' | 'female';
export type Step = 'workflow' | 'gender' | 'traits' | 'upload' | 'preview' | 'generate-multiple' | 'training';

// Stack-based navigation
export interface NavigationStep {
  step: Step;
  data?: any;
}

// Avatar creation state
export interface AvatarCreationState {
  workflowType: WorkflowType;
  selectedGender: Gender;
  selectedTraits: Record<string, string>;
  previewImage: string | null;
  generatedImages: string[];
  selectedImages: string[];
  trainingProgress: number;
  trainingStatus: string;
}
