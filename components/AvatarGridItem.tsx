import { MaterialIcons } from '@expo/vector-icons';
import { Image } from 'expo-image';
import React from 'react';
import { Dimensions, Pressable, StyleSheet, View } from 'react-native';
import { ActivityIndicator, Text, useTheme } from 'react-native-paper';
import { Avatar } from '../types';

const { width } = Dimensions.get('window');
const GRID_PADDING = 16;
const GRID_GAP = 12;
const COLUMNS = 3;
const ITEM_WIDTH = (width - GRID_PADDING * 2) / COLUMNS;

interface AvatarGridItemProps {
  avatar: Avatar;
  onPress: () => void;
}

export const AvatarGridItem: React.FC<AvatarGridItemProps> = ({
  avatar,
  onPress,
}) => {
  const theme = useTheme();

  return (
    <Pressable onPress={onPress} style={[styles.container, { width: ITEM_WIDTH }]}>
      <View style={styles.avatarContainer}>
        {avatar.images.length > 0 ? (
          <Image
            source={{ uri: avatar.images[0] }}
            style={[
              styles.avatarImage,
              { borderColor: theme.colors.outline }
            ]}
            contentFit="cover"
          />
        ) : (
          <View style={[
            styles.placeholderImage,
            {
              backgroundColor: theme.colors.surfaceVariant,
              borderColor: theme.colors.outline
            }
          ]}>
            <MaterialIcons
              name="face"
              size={32}
              color={theme.colors.onSurfaceVariant}
            />
          </View>
        )}

        {/* Training status indicator */}
        {avatar.isTraining && (
          <View style={[styles.statusIndicator, { backgroundColor: theme.colors.secondary }]}>
            <ActivityIndicator size={12} color={theme.colors.onSecondary} />
          </View>
        )}
      </View>

      <Text
        variant="labelLarge"
        numberOfLines={1}
        style={[styles.name, { color: theme.colors.onSurface }]}
      >
        {avatar.name}
      </Text>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: 6,
    marginBottom: GRID_GAP,
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: 8,
  },
  avatarImage: {
    width: ITEM_WIDTH - 24,
    height: ITEM_WIDTH - 24,
    borderRadius: (ITEM_WIDTH - 24) / 2,
    borderWidth: 2,
  },
  placeholderImage: {
    width: ITEM_WIDTH - 24,
    height: ITEM_WIDTH - 24,
    borderRadius: (ITEM_WIDTH - 24) / 2,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 18,
    height: 18,
    borderRadius: 9,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#fff',
  },
  name: {
    textAlign: 'center',
    fontWeight: '600',
    fontSize: 12,
    maxWidth: ITEM_WIDTH - 12,
  },
});
