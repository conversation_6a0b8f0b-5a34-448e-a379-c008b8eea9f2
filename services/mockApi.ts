import { ApiResponse, Avatar, CreateAvatarRequest, GeneratedImage, GenerateImageRequest } from '../types';

// Mock data with AI character avatars
const mockAvatars: Avatar[] = [
  {
    id: '1',
    name: '<PERSON><PERSON><PERSON>',
    description: 'The Copy Ninja - A skilled and mysterious shinobi known for his Sharingan eye and lightning techniques.',
    images: [
      'https://i.pravatar.cc/400?img=1',
      'https://i.pravatar.cc/400?img=2',
      'https://i.pravatar.cc/400?img=3',
      'https://i.pravatar.cc/400?img=4',
      'https://i.pravatar.cc/400?img=5',
    ],
    createdAt: new Date('2024-01-15'),
    isTraining: false,
  },
  {
    id: '2',
    name: '<PERSON><PERSON><PERSON>',
    description: 'The Seventh Hokage - A determined ninja with the Nine-Tails fox spirit and incredible willpower.',
    images: [
      'https://i.pravatar.cc/400?img=6',
      'https://i.pravatar.cc/400?img=7',
      'https://i.pravatar.cc/400?img=8',
      'https://i.pravatar.cc/400?img=9',
      'https://i.pravatar.cc/400?img=10',
      'https://i.pravatar.cc/400?img=11',
    ],
    createdAt: new Date('2024-01-20'),
    isTraining: true,
    trainingProgress: 75,
  },
  {
    id: '3',
    name: 'Sasuke',
    images: [
      'https://i.pravatar.cc/400?img=12',
      'https://i.pravatar.cc/400?img=13',
      'https://i.pravatar.cc/400?img=14',
      'https://i.pravatar.cc/400?img=15',
      'https://i.pravatar.cc/400?img=16',
    ],
    createdAt: new Date('2024-01-25'),
    isTraining: false,
  },
  {
    id: '4',
    name: 'Goku',
    images: [
      'https://i.pravatar.cc/400?img=17',
      'https://i.pravatar.cc/400?img=18',
      'https://i.pravatar.cc/400?img=19',
      'https://i.pravatar.cc/400?img=20',
      'https://i.pravatar.cc/400?img=21',
    ],
    createdAt: new Date('2024-02-01'),
    isTraining: false,
  },
  {
    id: '5',
    name: 'Vegeta',
    images: [
      'https://i.pravatar.cc/400?img=22',
      'https://i.pravatar.cc/400?img=23',
      'https://i.pravatar.cc/400?img=24',
      'https://i.pravatar.cc/400?img=25',
      'https://i.pravatar.cc/400?img=26',
    ],
    createdAt: new Date('2024-02-05'),
    isTraining: true,
    trainingProgress: 30,
  },
  {
    id: '6',
    name: 'Piccolo',
    images: [
      'https://i.pravatar.cc/400?img=27',
      'https://i.pravatar.cc/400?img=28',
      'https://i.pravatar.cc/400?img=29',
      'https://i.pravatar.cc/400?img=30',
      'https://i.pravatar.cc/400?img=31',
    ],
    createdAt: new Date('2024-02-08'),
    isTraining: false,
  },
  {
    id: '7',
    name: 'Deku',
    images: [
      'https://i.pravatar.cc/400?img=32',
      'https://i.pravatar.cc/400?img=33',
      'https://i.pravatar.cc/400?img=34',
      'https://i.pravatar.cc/400?img=35',
      'https://i.pravatar.cc/400?img=36',
    ],
    createdAt: new Date('2024-02-10'),
    isTraining: false,
  },
  {
    id: '8',
    name: 'Bakugo',
    images: [
      'https://i.pravatar.cc/400?img=37',
      'https://i.pravatar.cc/400?img=38',
      'https://i.pravatar.cc/400?img=39',
      'https://i.pravatar.cc/400?img=40',
      'https://i.pravatar.cc/400?img=41',
    ],
    createdAt: new Date('2024-02-12'),
    isTraining: true,
    trainingProgress: 90,
  },
  {
    id: '9',
    name: 'Todoroki',
    images: [
      'https://i.pravatar.cc/400?img=42',
      'https://i.pravatar.cc/400?img=43',
      'https://i.pravatar.cc/400?img=44',
      'https://i.pravatar.cc/400?img=45',
      'https://i.pravatar.cc/400?img=46',
    ],
    createdAt: new Date('2024-02-15'),
    isTraining: false,
  },
  {
    id: '10',
    name: 'Tanjiro',
    images: [
      'https://i.pravatar.cc/400?img=47',
      'https://i.pravatar.cc/400?img=48',
      'https://i.pravatar.cc/400?img=49',
      'https://i.pravatar.cc/400?img=50',
      'https://i.pravatar.cc/400?img=51',
    ],
    createdAt: new Date('2024-02-18'),
    isTraining: false,
  },
  {
    id: '11',
    name: 'Zenitsu',
    images: [
      'https://i.pravatar.cc/400?img=52',
      'https://i.pravatar.cc/400?img=53',
      'https://i.pravatar.cc/400?img=54',
      'https://i.pravatar.cc/400?img=55',
      'https://i.pravatar.cc/400?img=56',
    ],
    createdAt: new Date('2024-02-20'),
    isTraining: true,
    trainingProgress: 60,
  },
  {
    id: '12',
    name: 'Nezuko',
    images: [
      'https://i.pravatar.cc/400?img=57',
      'https://i.pravatar.cc/400?img=58',
      'https://i.pravatar.cc/400?img=59',
      'https://i.pravatar.cc/400?img=60',
      'https://i.pravatar.cc/400?img=61',
    ],
    createdAt: new Date('2024-02-22'),
    isTraining: false,
  },
  {
    id: '13',
    name: 'Luffy',
    images: [
      'https://i.pravatar.cc/400?img=62',
      'https://i.pravatar.cc/400?img=63',
      'https://i.pravatar.cc/400?img=64',
      'https://i.pravatar.cc/400?img=65',
      'https://i.pravatar.cc/400?img=66',
    ],
    createdAt: new Date('2024-02-25'),
    isTraining: true,
    trainingProgress: 85,
  },
  {
    id: '14',
    name: 'Zoro',
    images: [
      'https://i.pravatar.cc/400?img=67',
      'https://i.pravatar.cc/400?img=68',
      'https://i.pravatar.cc/400?img=69',
      'https://i.pravatar.cc/400?img=70',
      'https://i.pravatar.cc/400?img=71',
    ],
    createdAt: new Date('2024-02-26'),
    isTraining: false,
  },
  {
    id: '15',
    name: 'Sanji',
    images: [
      'https://i.pravatar.cc/400?img=72',
      'https://i.pravatar.cc/400?img=73',
      'https://i.pravatar.cc/400?img=74',
      'https://i.pravatar.cc/400?img=75',
      'https://i.pravatar.cc/400?img=76',
    ],
    createdAt: new Date('2024-02-28'),
    isTraining: true,
    trainingProgress: 40,
  },
  {
    id: '16',
    name: 'Nami',
    images: [
      'https://i.pravatar.cc/400?img=77',
      'https://i.pravatar.cc/400?img=78',
      'https://i.pravatar.cc/400?img=79',
      'https://i.pravatar.cc/400?img=80',
      'https://i.pravatar.cc/400?img=81',
    ],
    createdAt: new Date('2024-03-01'),
    isTraining: false,
  },
  {
    id: '17',
    name: 'Ichigo',
    images: [
      'https://i.pravatar.cc/400?img=82',
      'https://i.pravatar.cc/400?img=83',
      'https://i.pravatar.cc/400?img=84',
      'https://i.pravatar.cc/400?img=85',
      'https://i.pravatar.cc/400?img=86',
    ],
    createdAt: new Date('2024-03-03'),
    isTraining: true,
    trainingProgress: 70,
  },
  {
    id: '18',
    name: 'Rukia',
    images: [
      'https://i.pravatar.cc/400?img=87',
      'https://i.pravatar.cc/400?img=88',
      'https://i.pravatar.cc/400?img=89',
      'https://i.pravatar.cc/400?img=90',
      'https://i.pravatar.cc/400?img=91',
    ],
    createdAt: new Date('2024-03-05'),
    isTraining: false,
  },
  {
    id: '19',
    name: 'Edward',
    images: [
      'https://i.pravatar.cc/400?img=92',
      'https://i.pravatar.cc/400?img=93',
      'https://i.pravatar.cc/400?img=94',
      'https://i.pravatar.cc/400?img=95',
      'https://i.pravatar.cc/400?img=96',
    ],
    createdAt: new Date('2024-03-07'),
    isTraining: true,
    trainingProgress: 55,
  },
  {
    id: '20',
    name: 'Alphonse',
    images: [
      'https://i.pravatar.cc/400?img=97',
      'https://i.pravatar.cc/400?img=98',
      'https://i.pravatar.cc/400?img=99',
      'https://i.pravatar.cc/400?img=100',
      'https://i.pravatar.cc/400?img=101',
    ],
    createdAt: new Date('2024-03-08'),
    isTraining: false,
  },
  {
    id: '21',
    name: 'Natsu',
    images: [
      'https://i.pravatar.cc/400?img=102',
      'https://i.pravatar.cc/400?img=103',
      'https://i.pravatar.cc/400?img=104',
      'https://i.pravatar.cc/400?img=105',
      'https://i.pravatar.cc/400?img=106',
    ],
    createdAt: new Date('2024-03-10'),
    isTraining: true,
    trainingProgress: 25,
  },
  {
    id: '22',
    name: 'Lucy',
    images: [
      'https://i.pravatar.cc/400?img=107',
      'https://i.pravatar.cc/400?img=108',
      'https://i.pravatar.cc/400?img=109',
      'https://i.pravatar.cc/400?img=110',
      'https://i.pravatar.cc/400?img=111',
    ],
    createdAt: new Date('2024-03-12'),
    isTraining: false,
  },
];

const mockGeneratedImages: GeneratedImage[] = [
  {
    id: '1',
    avatarId: '1',
    imageUri: 'https://picsum.photos/400/400?random=10',
    prompt: 'Professional headshot in office setting',
    createdAt: new Date('2024-01-25'),
  },
  {
    id: '2',
    avatarId: '1',
    imageUri: 'https://picsum.photos/400/400?random=11',
    prompt: 'Casual portrait with natural lighting',
    createdAt: new Date('2024-01-26'),
  },
  {
    id: '3',
    avatarId: '1',
    imageUri: 'https://picsum.photos/400/400?random=12',
    prompt: 'Action pose in dynamic setting',
    createdAt: new Date('2024-01-27'),
  },
  {
    id: '4',
    avatarId: '1',
    imageUri: 'https://picsum.photos/400/400?random=13',
    prompt: 'Artistic black and white portrait',
    createdAt: new Date('2024-01-28'),
  },
  {
    id: '5',
    avatarId: '1',
    imageUri: 'https://picsum.photos/400/400?random=14',
    prompt: 'Outdoor adventure scene',
    createdAt: new Date('2024-01-29'),
  },
  {
    id: '6',
    avatarId: '1',
    imageUri: 'https://picsum.photos/400/400?random=15',
    prompt: 'Formal business attire portrait',
    createdAt: new Date('2024-01-30'),
  },
];

// Simulate API delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export const mockApi = {
  // Avatar management
  getAvatars: async (): Promise<ApiResponse<Avatar[]>> => {
    await delay(1000);
    return {
      data: mockAvatars,
      success: true,
    };
  },

  createAvatar: async (request: CreateAvatarRequest): Promise<ApiResponse<Avatar>> => {
    await delay(2000);
    const newAvatar: Avatar = {
      id: Date.now().toString(),
      name: request.name,
      description: request.description,
      images: request.images,
      createdAt: new Date(),
      isTraining: true,
      trainingProgress: 0,
    };
    mockAvatars.push(newAvatar);
    
    // Simulate training progress
    setTimeout(() => {
      newAvatar.trainingProgress = 100;
      newAvatar.isTraining = false;
    }, 5000);
    
    return {
      data: newAvatar,
      success: true,
      message: 'Avatar created successfully. Training started.',
    };
  },

  deleteAvatar: async (avatarId: string): Promise<ApiResponse<boolean>> => {
    await delay(500);
    const index = mockAvatars.findIndex(a => a.id === avatarId);
    if (index > -1) {
      mockAvatars.splice(index, 1);
      return {
        data: true,
        success: true,
        message: 'Avatar deleted successfully',
      };
    }
    return {
      data: false,
      success: false,
      message: 'Avatar not found',
    };
  },

  // Image generation
  generateImage: async (request: GenerateImageRequest): Promise<ApiResponse<GeneratedImage>> => {
    await delay(3000);
    const newImage: GeneratedImage = {
      id: Date.now().toString(),
      avatarId: request.avatarId,
      imageUri: `https://picsum.photos/400/400?random=${Math.floor(Math.random() * 1000)}`,
      prompt: request.prompt,
      createdAt: new Date(),
    };
    mockGeneratedImages.push(newImage);
    
    return {
      data: newImage,
      success: true,
      message: 'Image generated successfully',
    };
  },

  getGeneratedImages: async (avatarId?: string): Promise<ApiResponse<GeneratedImage[]>> => {
    await delay(800);
    const images = avatarId 
      ? mockGeneratedImages.filter(img => img.avatarId === avatarId)
      : mockGeneratedImages;
    
    return {
      data: images,
      success: true,
    };
  },
};
