import * as ImagePicker from 'expo-image-picker';
import { Alert } from 'react-native';

export interface ImageValidationResult {
  isValid: boolean;
  errors: string[];
}

export const validateImages = (images: string[]): ImageValidationResult => {
  const errors: string[] = [];

  if (images.length < 5) {
    errors.push('At least 5 images are required for optimal training results');
  }

  if (images.length > 10) {
    errors.push('Maximum 10 images allowed');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

export const requestImagePermissions = async (): Promise<boolean> => {
  const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
  
  if (status !== 'granted') {
    Alert.alert(
      'Permission Required',
      'Please grant access to your photo library to upload images for avatar training.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Open Settings', onPress: () => ImagePicker.requestMediaLibraryPermissionsAsync() }
      ]
    );
    return false;
  }
  
  return true;
};

export const pickMultipleImages = async (maxImages: number = 10): Promise<string[]> => {
  const hasPermission = await requestImagePermissions();
  if (!hasPermission) return [];

  try {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsMultipleSelection: true,
      quality: 0.8,
      selectionLimit: maxImages,
      allowsEditing: false,
    });

    if (!result.canceled) {
      return result.assets.map(asset => asset.uri);
    }
  } catch (error) {
    Alert.alert('Error', 'Failed to pick images. Please try again.');
  }

  return [];
};

export const getImageDimensions = async (uri: string): Promise<{ width: number; height: number } | null> => {
  return new Promise((resolve) => {
    const image = new Image();
    image.onload = () => {
      resolve({ width: image.width, height: image.height });
    };
    image.onerror = () => {
      resolve(null);
    };
    image.src = uri;
  });
};

export const formatImageCount = (count: number): string => {
  if (count === 0) return 'No images';
  if (count === 1) return '1 image';
  return `${count} images`;
};

export const getImageQualityTips = (): string[] => {
  return [
    'Use high-resolution images (at least 512x512 pixels)',
    'Include photos from different angles and lighting conditions',
    'Ensure the subject is clearly visible and well-lit',
    'Avoid blurry or heavily filtered images',
    'Include close-up and medium shots for best results',
  ];
};
